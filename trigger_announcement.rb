#!/usr/bin/env ruby

# Script to manually trigger a delayed announcement to be posted

puts "=== Triggering Delayed Announcement ==="

# Find the announcement
announcement = Announcement.find(11)
puts "Found announcement: #{announcement.title}"
puts "Current workflow_state: #{announcement.workflow_state}"
puts "delayed_post_at: #{announcement.delayed_post_at}"

if announcement.workflow_state == 'post_delayed'
  puts "\nTriggering delayed post..."
  announcement.delayed_post
  announcement.reload
  
  puts "New workflow_state: #{announcement.workflow_state}"
  puts "posted_at: #{announcement.posted_at}"
  
  # Test the query again
  user_course_ids = [20]
  start_date = 14.days.ago.beginning_of_day
  end_date = start_date + 28.days
  
  course_announcements = Announcement.where(context_type: 'Course', context_id: user_course_ids)
                                     .where(workflow_state: ['active', 'post_delayed'])
                                     .where("workflow_state = 'active' OR (workflow_state = 'post_delayed' AND (unlock_at IS NULL OR unlock_at <= ?) AND (delayed_post_at IS NULL OR delayed_post_at <= ?))", Time.now.utc, Time.now.utc)
                                     .ordered_between(start_date, end_date)
                                     .preload(:context)
                                     .limit(20)
  
  puts "\nQuery result after triggering: #{course_announcements.count} announcements"
else
  puts "\nAnnouncement is not in post_delayed state, no action needed."
end

puts "\n=== End ==="
