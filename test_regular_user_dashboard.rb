#!/usr/bin/env ruby

# Script to test dashboard behavior for regular users (non-admins)

puts "=== Testing Dashboard for Regular Users ==="

# Find a student user or create one for testing
course = Course.find(20)
student_enrollment = course.enrollments.where(type: 'StudentEnrollment').first

if student_enrollment
  student = student_enrollment.user
  puts "Testing with existing student: #{student.id}"
else
  # Create a test student if none exists
  student = User.create!(name: "Test Student")
  course.enroll_student(student, enrollment_state: 'active')
  puts "Created test student: #{student.id}"
end

# Test permissions
puts "\nStudent permissions in course #{course.id}:"
puts "  view_unpublished_items: #{course.grants_right?(student, :view_unpublished_items)}"
puts "  read_as_admin: #{course.grants_right?(student, :read_as_admin)}"
puts "  moderate_forum: #{course.grants_right?(student, :moderate_forum)}"

# Simulate dashboard logic for student
user_courses = student.courses.active.where.not(workflow_state: 'deleted')
puts "\nStudent has #{user_courses.count} active courses"

if user_courses.any?
  start_date = 14.days.ago.beginning_of_day
  end_date = start_date + 28.days
  
  # Check if user can view unpublished items in any of their courses
  include_unpublished = user_courses.any? { |course| course.grants_right?(student, :view_unpublished_items) }
  puts "include_unpublished: #{include_unpublished}"
  
  scope = Announcement.where(context_type: 'Course', context_id: user_courses.pluck(:id))
  
  if include_unpublished
    scope = scope.where.not(workflow_state: "deleted")
    puts "Admin scope: #{scope.count} announcements"
  else
    scope = scope.where(workflow_state: ['active', 'post_delayed'])
                 .where("workflow_state = 'active' OR (workflow_state = 'post_delayed' AND (unlock_at IS NULL OR unlock_at <= ?) AND (delayed_post_at IS NULL OR delayed_post_at <= ?))", Time.now.utc, Time.now.utc)
    puts "Regular user scope: #{scope.count} announcements"
  end
  
  course_announcements = scope.ordered_between(start_date, end_date)
                              .preload(:context)
                              .limit(20)
  
  puts "\nFinal result for student: #{course_announcements.count} announcements"
  course_announcements.each do |ann|
    puts "  ID: #{ann.id}, Title: #{ann.title}, Workflow: #{ann.workflow_state}"
  end
end

puts "\n=== End ==="
