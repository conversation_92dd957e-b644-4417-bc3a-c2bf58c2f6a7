#!/usr/bin/env ruby

# Script to create a test announcement that should show up immediately

puts "=== Creating Test Announcement ==="

course = Course.find(20)
puts "Creating announcement for course: #{course.name} (ID: #{course.id})"

# Create an active announcement (no delayed posting)
announcement = course.announcements.create!(
  title: "Test Active Announcement",
  message: "<p>This is a test announcement that should show up immediately.</p>",
  workflow_state: "active",
  posted_at: Time.now,
  user_id: 55  # Same user from the logs
)

puts "Created announcement: #{announcement.title} (ID: #{announcement.id})"
puts "  workflow_state: #{announcement.workflow_state}"
puts "  posted_at: #{announcement.posted_at}"

# Test the query
user_course_ids = [20]
start_date = 14.days.ago.beginning_of_day
end_date = start_date + 28.days

course_announcements = Announcement.where(context_type: 'Course', context_id: user_course_ids)
                                   .where(workflow_state: ['active', 'post_delayed'])
                                   .where("workflow_state = 'active' OR (workflow_state = 'post_delayed' AND (unlock_at IS NULL OR unlock_at <= ?) AND (delayed_post_at IS NULL OR delayed_post_at <= ?))", Time.now.utc, Time.now.utc)
                                   .ordered_between(start_date, end_date)
                                   .preload(:context)
                                   .limit(20)

puts "\nQuery result: #{course_announcements.count} announcements found"
course_announcements.each do |ann|
  puts "  ID: #{ann.id}, Title: #{ann.title}, Workflow: #{ann.workflow_state}"
end

puts "\n=== End ==="
