#!/usr/bin/env ruby

# Script to clear a user's closed notifications (for testing purposes)

puts "=== Clearing User's Closed Notifications ==="

user = User.find(55)
puts "User #{user.id} currently has closed notifications: #{user.get_preference(:closed_notifications)}"

# Clear the closed notifications
user.set_preference(:closed_notifications, [])
user.save!

puts "Cleared closed notifications for user #{user.id}"
puts "User #{user.id} now has closed notifications: #{user.get_preference(:closed_notifications)}"

# Test the account notifications again
account = Account.find(62)
account_announcements = AccountNotification.for_user_and_account(user, account)
puts "\nAfter clearing: Found #{account_announcements.count} account announcements"
account_announcements.each do |ann|
  puts "  ID: #{ann.id}, Subject: #{ann.subject}"
end

# Test the course announcements query
user_course_ids = [20]
start_date = 14.days.ago.beginning_of_day
end_date = start_date + 28.days

course_announcements = Announcement.where(context_type: 'Course', context_id: user_course_ids)
                                   .where(workflow_state: ['active', 'post_delayed'])
                                   .where("workflow_state = 'active' OR (workflow_state = 'post_delayed' AND (unlock_at IS NULL OR unlock_at <= ?) AND (delayed_post_at IS NULL OR delayed_post_at <= ?))", Time.now.utc, Time.now.utc)
                                   .ordered_between(start_date, end_date)
                                   .preload(:context)
                                   .limit(20)

puts "\nCourse announcements: #{course_announcements.count}"

puts "\n=== End ==="
