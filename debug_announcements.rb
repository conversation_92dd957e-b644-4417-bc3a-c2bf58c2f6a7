#!/usr/bin/env ruby

# Debug script to understand why the announcement query returns zero results

puts "=== Debugging Announcement Query ==="

# Check raw database values
puts "\n1. Raw database values:"
raw_results = ActiveRecord::Base.connection.execute("SELECT id, context_type, context_id, workflow_state, unlock_at, delayed_post_at, posted_at, created_at FROM discussion_topics WHERE type = 'Announcement'")
puts "  Total announcements in database: #{raw_results.count}"
raw_results.each do |row|
  puts "  ID: #{row['id']}, Context: #{row['context_type']}_#{row['context_id']}, Workflow: #{row['workflow_state']}"
  puts "    unlock_at: #{row['unlock_at']}"
  puts "    delayed_post_at: #{row['delayed_post_at']}"
  puts "    posted_at: #{row['posted_at']}"
  puts "    created_at: #{row['created_at']}"
  puts "    ---"
end

# Check all discussion topics to see if there are more
puts "\n1b. All discussion topics:"
all_topics = ActiveRecord::Base.connection.execute("SELECT id, type, context_type, context_id, workflow_state FROM discussion_topics")
puts "  Total discussion topics: #{all_topics.count}"
all_topics.each do |row|
  puts "  ID: #{row['id']}, Type: #{row['type']}, Context: #{row['context_type']}_#{row['context_id']}, Workflow: #{row['workflow_state']}"
end

# Check account notifications (global announcements)
puts "\n1c. Account notifications:"
account_notifications = ActiveRecord::Base.connection.execute("SELECT id, subject, message, account_id, start_at, end_at FROM account_notifications")
puts "  Total account notifications: #{account_notifications.count}"
account_notifications.each do |row|
  puts "  ID: #{row['id']}, Subject: #{row['subject']}, Account: #{row['account_id']}"
  puts "    start_at: #{row['start_at']}, end_at: #{row['end_at']}"
end

# Check user's closed notifications preference
puts "\n1d. User's closed notifications:"
user = User.find(55)  # Based on the logs showing user 55
closed_notifications = user.get_preference(:closed_notifications) || []
puts "  User #{user.id} has #{closed_notifications.count} closed notifications: #{closed_notifications}"

# Test AccountNotification.for_user_and_account
puts "\n1e. Testing AccountNotification.for_user_and_account:"
account = Account.find(62)  # Based on the logs showing account 62
account_announcements = AccountNotification.for_user_and_account(user, account)
puts "  Found #{account_announcements.count} account announcements for user #{user.id}"
account_announcements.each do |ann|
  puts "    ID: #{ann.id}, Subject: #{ann.subject}"
end

# Check all announcements through Rails
puts "\n2. All Announcements through Rails:"
Announcement.all.each do |a|
  puts "  ID: #{a.id}, Context: #{a.context_type}_#{a.context_id}, Workflow: #{a.workflow_state}"
  puts "    unlock_at: #{a.unlock_at}"
  puts "    delayed_post_at: #{a.delayed_post_at}"
  puts "    posted_at: #{a.posted_at}"
  puts "    created_at: #{a.created_at}"
  coalesce_value = [a.unlock_at, a.delayed_post_at, a.posted_at, a.created_at].compact.first
  puts "    COALESCE value: #{coalesce_value}"
  puts "    ---"
end

# Check date range
puts "\n3. Date range being used:"
start_date = 14.days.ago.beginning_of_day
end_date = start_date + 28.days
puts "  start_date: #{start_date}"
puts "  end_date: #{end_date}"
puts "  Current time: #{Time.now}"

# Check if announcements fall within date range
puts "\n4. Date range check:"
Announcement.all.each do |a|
  coalesce_value = [a.unlock_at, a.delayed_post_at, a.posted_at, a.created_at].compact.first
  in_range = coalesce_value >= start_date && coalesce_value <= end_date
  puts "  Announcement #{a.id}: COALESCE=#{coalesce_value}, in_range=#{in_range}"
end

# Test the between scope
puts "\n5. Testing between scope:"
between_results = Announcement.between(start_date, end_date)
puts "  Announcement.between(#{start_date}, #{end_date}) count: #{between_results.count}"

# Test the ordered_between scope
puts "\n6. Testing ordered_between scope:"
ordered_between_results = Announcement.ordered_between(start_date, end_date)
puts "  Announcement.ordered_between(#{start_date}, #{end_date}) count: #{ordered_between_results.count}"

# Test workflow_state filter
puts "\n7. Testing workflow_state filter:"
active_announcements = Announcement.where(workflow_state: 'active')
puts "  Announcement.where(workflow_state: 'active') count: #{active_announcements.count}"

# Test context_type and context_id filter
puts "\n8. Testing context filters:"
course_announcements = Announcement.where(context_type: 'Course')
puts "  Announcement.where(context_type: 'Course') count: #{course_announcements.count}"

# Test the full query step by step
puts "\n9. Testing full query step by step:"

# Simulate user courses (assuming user has access to course 20)
user_course_ids = [20]  # Based on the data shown in images
puts "  user_course_ids: #{user_course_ids}"

step1 = Announcement.where(context_type: 'Course', context_id: user_course_ids)
puts "  Step 1 - context filter: #{step1.count}"

step2 = step1.where(workflow_state: 'active')
puts "  Step 2 - workflow_state filter (old): #{step2.count}"

# Test the new workflow_state filter
step2_new = step1.where(workflow_state: ['active', 'post_delayed'])
                 .where("workflow_state = 'active' OR (workflow_state = 'post_delayed' AND (unlock_at IS NULL OR unlock_at <= ?) AND (delayed_post_at IS NULL OR delayed_post_at <= ?))", Time.now.utc, Time.now.utc)
puts "  Step 2 - workflow_state filter (new): #{step2_new.count}"

step3 = step2_new.ordered_between(start_date, end_date)
puts "  Step 3 - date range filter: #{step3.count}"

# Show the actual SQL being generated
puts "\n10. Generated SQL:"
puts step3.to_sql

# Test what happens if current time is after delayed_post_at
puts "\n11. Testing with simulated future time:"
future_time = Time.parse("2025-08-05 15:00:00 +0800")  # 3 PM, after the delayed post time
puts "  Simulated time: #{future_time}"

# Test the new workflow_state filter with future time
step2_future = step1.where(workflow_state: ['active', 'post_delayed'])
                   .where("workflow_state = 'active' OR (workflow_state = 'post_delayed' AND (unlock_at IS NULL OR unlock_at <= ?) AND (delayed_post_at IS NULL OR delayed_post_at <= ?))", future_time.utc, future_time.utc)
puts "  Step 2 - workflow_state filter (future time): #{step2_future.count}"

step3_future = step2_future.ordered_between(start_date, end_date)
puts "  Step 3 - date range filter (future time): #{step3_future.count}"

puts "\n=== End Debug ==="
