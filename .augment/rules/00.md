---
type: "manual"
---

1. DO NOT RUN A SCRIPT THAT REMOVES EXISTING ROWS FROM ANY TABLE THAT WAS CREATED BY THE USER UNLESS PROMPTED UPON
2. DO NOT RUN TESTS THAT HAS A COMMAND OR SCRIPT THAT REMOVES EXISTING ROWS FROM ANY TABLE THAT WAS INSERTED OR CREATED BY THE USER UNLESS PROMPTED UPON.
3. As much as possible avoid the <View> ('@instructure/ui-view') component as the gap, justifyContent and alignItems does not work. Maybe try to use div instead. 
4. In order to run the correct node.js version, use the nvm use 22 to get the latest version of node installed.