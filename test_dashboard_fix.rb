#!/usr/bin/env ruby

# Script to test the dashboard fix

puts "=== Testing Dashboard Fix ==="

# Simulate the dashboard logic
user = User.find(55)
user_courses = user.courses.active.where.not(workflow_state: 'deleted')

puts "User #{user.id} has #{user_courses.count} active courses"

if user_courses.any?
  start_date = 14.days.ago.beginning_of_day
  end_date = start_date + 28.days
  
  puts "Date range: #{start_date} to #{end_date}"
  
  # Check if user can view unpublished items in any of their courses
  include_unpublished = user_courses.any? { |course| course.grants_right?(user, :view_unpublished_items) }
  puts "include_unpublished: #{include_unpublished}"
  
  scope = Announcement.where(context_type: 'Course', context_id: user_courses.pluck(:id))
  puts "Base scope: #{scope.count} announcements"
  
  if include_unpublished
    # For admins/teachers: show all announcements except deleted ones (like announcements page)
    scope = scope.where.not(workflow_state: "deleted")
    puts "Admin scope (not deleted): #{scope.count} announcements"
  else
    # For regular users: only show active announcements or post_delayed ones that have passed their time
    scope = scope.where(workflow_state: ['active', 'post_delayed'])
                 .where("workflow_state = 'active' OR (workflow_state = 'post_delayed' AND (unlock_at IS NULL OR unlock_at <= ?) AND (delayed_post_at IS NULL OR delayed_post_at <= ?))", Time.now.utc, Time.now.utc)
    puts "Regular user scope: #{scope.count} announcements"
  end
  
  course_announcements = scope.ordered_between(start_date, end_date)
                              .preload(:context)
                              .limit(20)
  
  puts "\nFinal result: #{course_announcements.count} announcements"
  course_announcements.each do |ann|
    puts "  ID: #{ann.id}, Title: #{ann.title}, Workflow: #{ann.workflow_state}"
    puts "    delayed_post_at: #{ann.delayed_post_at}"
    puts "    Context: #{ann.context.name}"
  end
else
  puts "No active courses found"
end

puts "\n=== End ==="
